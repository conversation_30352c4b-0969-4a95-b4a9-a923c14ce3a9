import { Metadata } from "next"
import { Suspense } from "react";
import { ProfileHeader } from "@/components/profile/profile-header"
import { ProfileTabs } from "@/components/profile/profile-tabs"
import { createSupabaseServerClient } from "@/lib/supabase/client"
import { redirect } from "next/navigation"

export const metadata: Metadata = {
  title: "Your Profile | Introducing.day",
  description: "View and manage your Introducing.day profile",
}

export default async function ProfilePage() {
  const supabase = createSupabaseServerClient()
  
  // Get the current user session
  const { data: { session } } = await supabase.auth.getSession()
  
  // 移除重定向逻辑，允许未登录用户查看页面
  // if (!session) {
  //   redirect("/signin")
  // }
  
  // Get user data (可能为空)
  let user = null
  let upvotedProducts = []
  
  if (session) {
    // 用户已登录，获取用户数据
    const { data: userData } = await supabase.auth.getUser()
    user = userData.user
    
    // 获取用户点赞的产品
    const { data: userVotes } = await supabase
      .from("user_votes")
      .select("product_id")
      .eq("user_id", user?.id)
    
    // 获取用户点赞的产品详情
    const upvotedProductIds = userVotes?.map(vote => vote.product_id) || []
    
    if (upvotedProductIds.length > 0) {
      const { data: products } = await supabase
        .from("products")
        .select("*")
        .in("id", upvotedProductIds)
      
      upvotedProducts = products || []
    }
  } else {
    // 用户未登录，获取一些示例产品作为演示
    const { data: sampleProducts } = await supabase
      .from("products")
      .select("*")
      .limit(3)
    
    upvotedProducts = sampleProducts || []
  }
  
  return (
    <div className="container py-8 max-w-5xl">
      <ProfileHeader user={user} />
      <Suspense fallback={<div className="text-center p-8">Loading tabs...</div>}>
        <ProfileTabs user={user} upvotedProducts={upvotedProducts} />
      </Suspense>
    </div>
  )
}
