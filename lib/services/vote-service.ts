import { createSupabaseClient } from "@/lib/supabase/client"
import { User } from "@supabase/supabase-js"

// 获取当前登录用户
const getCurrentUser = async (): Promise<User | null> => {
  try {
    const supabase = createSupabaseClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      console.error('获取当前用户失败:', error)
      return null
    }
    
    return user
  } catch (err) {
    console.error('获取当前用户时出现异常:', err)
    return null
  }
}

// 获取产品的点赞数
export const getProductVotes = async (productId: number): Promise<number> => {
  try {
    const supabase = createSupabaseClient()
    const { data, error } = await supabase
      .from('product_votes')
      .select('upvotes')
      .eq('product_id', productId)
      .maybeSingle() // 使用 maybeSingle 而不是 single，这样当没有记录时不会抛出错误
    
    if (error) {
      console.error('获取点赞数失败:', error)
      return 0
    }
    
    return data?.upvotes || 0
  } catch (err) {
    console.error('获取点赞数出现异常:', err)
    return 0
  }
}

// 检查用户是否已点赞
export const hasUserVoted = async (productId: number): Promise<boolean> => {
  try {
    const user = await getCurrentUser()
    if (!user) return false
    
    const supabase = createSupabaseClient()
    const { data, error } = await supabase
      .from('user_votes')
      .select('id')
      .eq('product_id', productId)
      .eq('user_id', user.id)
      .maybeSingle() // 使用 maybeSingle 而不是 single
    
    if (error) {
      console.error('检查用户点赞状态失败:', error)
      return false
    }
    
    return !!data
  } catch (err) {
    console.error('检查用户点赞状态出现异常:', err)
    return false
  }
}

// 添加点赞
export const addVote = async (productId: number): Promise<{ success: boolean, message: string }> => {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return { 
        success: false, 
        message: '请先登录后再点赞' 
      }
    }
    
    const supabase = createSupabaseClient()
    
    // 检查用户是否已经点赞
    const alreadyVoted = await hasUserVoted(productId)
    if (alreadyVoted) {
      return { 
        success: false, 
        message: '您已经点赞过这个产品' 
      }
    }
    
    // 先检查产品是否存在于 product_votes 表中
    const { data: existingVote } = await supabase
      .from('product_votes')
      .select('product_id')
      .eq('product_id', productId)
      .maybeSingle()
    
    // 如果产品不存在于 product_votes 表中，则创建一个新记录
    if (!existingVote) {
      await supabase
        .from('product_votes')
        .insert({
          product_id: productId,
          upvotes: 0
        })
    }
    
    // 添加点赞记录
    const { error } = await supabase
      .from('user_votes')
      .insert({
        product_id: productId,
        user_id: user.id
      })
    
    if (error) {
      console.error('添加点赞失败:', error)
      return { 
        success: false, 
        message: '点赞失败，请稍后再试' 
      }
    }
    
    return { 
      success: true, 
      message: '点赞成功' 
    }
  } catch (err) {
    console.error('添加点赞过程中出现异常:', err)
    return { 
      success: false, 
      message: '点赞过程中出现异常，请稍后再试' 
    }
  }
}

// 移除点赞
export const removeVote = async (productId: number): Promise<{ success: boolean, message: string }> => {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return { 
        success: false, 
        message: '请先登录后再操作' 
      }
    }
    
    const supabase = createSupabaseClient()
    
    // 检查用户是否已经点赞
    const hasVoted = await hasUserVoted(productId)
    if (!hasVoted) {
      return { 
        success: false, 
        message: '您还没有点赞过这个产品' 
      }
    }
    
    // 移除点赞记录
    const { error } = await supabase
      .from('user_votes')
      .delete()
      .eq('product_id', productId)
      .eq('user_id', user.id)
    
    if (error) {
      console.error('移除点赞失败:', error)
      return { 
        success: false, 
        message: '操作失败，请稍后再试' 
      }
    }
    
    return { 
      success: true, 
      message: '已取消点赞' 
    }
  } catch (err) {
    console.error('移除点赞过程中出现异常:', err)
    return { 
      success: false, 
      message: '取消点赞过程中出现异常，请稍后再试' 
    }
  }
}
